{"name": "root", "version": "1.0.0", "private": true, "engines": {"node": "20 || 22"}, "scripts": {"start": "NODE_OPTIONS=\"--no-node-snapshot --enable-source-maps\" backstage-cli package start repo start", "build:backend": "yarn workspace backend build", "build:all": "backstage-cli repo build --all", "build-image": "yarn workspace backend build-image", "tsc": "tsc", "tsc:full": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON> false --incremental false", "clean": "backstage-cli repo clean", "test": "backstage-cli repo test", "test:all": "backstage-cli repo test --coverage", "test:e2e": "playwright test", "fix": "backstage-cli repo fix", "lint": "backstage-cli repo lint --since origin/master", "lint:all": "backstage-cli repo lint", "prettier:check": "prettier --check .", "prettier:write": "prettier --write .", "new": "backstage-cli new"}, "workspaces": {"packages": ["packages/*", "plugins/*", "bhvr-template/skeleton"]}, "devDependencies": {"@backstage/cli": "^0.33.0", "@backstage/e2e-test-utils": "^0.1.1", "@playwright/test": "^1.32.3", "@types/react-dom": "^19.1.6", "node-gyp": "^10.0.0", "prettier": "^2.3.2", "typescript": "~5.8.0"}, "resolutions": {"@types/react": "^18", "@types/react-dom": "^18"}, "prettier": "@backstage/cli/config/prettier", "lint-staged": {"*.{js,jsx,ts,tsx,mjs,cjs}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "packageManager": "yarn@4.4.1", "dependencies": {"@mui/icons-material": "^7.2.0"}}