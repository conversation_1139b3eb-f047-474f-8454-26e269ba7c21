/* [next]/internal/font/google/geist_e531dabc.module.css [app-client] (css) */
@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/973faccb4f6aedb5-s.b7d310ad.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/d26cc22533d232c7-s.81df3a5b.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/b0a57561b6cb5495-s.p.da1ebef7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Fallback;
  src: local(Arial);
  ascent-override: 95.94%;
  descent-override: 28.16%;
  line-gap-override: 0.0%;
  size-adjust: 104.76%;
}

.geist_e531dabc-module__QGiZLq__className {
  font-family: Geist, Geist Fallback;
  font-style: normal;
}

.geist_e531dabc-module__QGiZLq__variable {
  --font-geist-sans: "Geist", "Geist Fallback";
}

/* [next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css) */
@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/942c7eecbf9bc714-s.cb6bbcb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/e5e2a9f48cda0a81-s.e32db976.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/8ee3a1ba4ed5baee-s.p.be19f591.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Mono Fallback;
  src: local(Arial);
  ascent-override: 74.67%;
  descent-override: 21.92%;
  line-gap-override: 0.0%;
  size-adjust: 134.59%;
}

.geist_mono_68a01160-module__YLcDdW__className {
  font-family: Geist Mono, Geist Mono Fallback;
  font-style: normal;
}

.geist_mono_68a01160-module__YLcDdW__variable {
  --font-geist-mono: "Geist Mono", "Geist Mono Fallback";
}

/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after {
      --tw-border-style: solid;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
    }

    ::backdrop {
      --tw-border-style: solid;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: var(--font-sans);
    --font-serif: var(--font-serif);
    --font-mono: var(--font-mono);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --tracking-normal: 0em;
    --radius-sm: calc(var(--radius)  - 4px);
    --radius-md: calc(var(--radius)  - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius)  + 4px);
    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --shadow: var(--shadow);
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
  }
}

@layer base {
  *, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::-webkit-file-upload-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::-webkit-file-upload-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-moz-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-file-upload-button {
    margin-right: 4px;
  }

  :not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-file-upload-button {
    margin-left: 4px;
  }

  :-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  :is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  input:where([type="button"], [type="reset"], [type="submit"]) {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::file-selector-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  * {
    border-color: var(--border);
    outline-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    * {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}

@layer components;

@layer utilities {
  .row-start-2 {
    grid-row-start: 2;
  }

  .row-start-3 {
    grid-row-start: 3;
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .inline {
    display: inline;
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-full {
    width: 100%;
  }

  .list-inside {
    list-style-position: inside;
  }

  .list-decimal {
    list-style-type: decimal;
  }

  .grid-rows-\[20px_1fr_20px\] {
    grid-template-rows: 20px 1fr 20px;
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-items-center {
    justify-items: center;
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-16 {
    gap: calc(var(--spacing) * 16);
  }

  .gap-\[24px\] {
    gap: 24px;
  }

  .gap-\[32px\] {
    gap: 32px;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }

  .border-black\/\[\.08\] {
    border-color: rgba(0, 0, 0, .08);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-black\/\[\.08\] {
      border-color: color-mix(in oklab, var(--color-black) 8%, transparent);
    }
  }

  .border-transparent {
    border-color: rgba(0, 0, 0, 0);
  }

  .bg-black\/\[\.05\] {
    background-color: rgba(0, 0, 0, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/\[\.05\] {
      background-color: color-mix(in oklab, var(--color-black) 5%, transparent);
    }
  }

  .bg-foreground {
    background-color: var(--foreground);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .pb-20 {
    padding-bottom: calc(var(--spacing) * 20);
  }

  .text-center {
    text-align: center;
  }

  .font-mono {
    font-family: var(--font-mono);
  }

  .font-sans {
    font-family: var(--font-sans);
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-sm\/6 {
    font-size: var(--text-sm);
    line-height: calc(var(--spacing) * 6);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-\[-\.01em\] {
    --tw-tracking: -.01em;
    letter-spacing: -.01em;
  }

  .text-background {
    color: var(--background);
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  @media (hover: hover) {
    .hover\:border-transparent:hover {
      border-color: rgba(0, 0, 0, 0);
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#383838\]:hover {
      background-color: #383838;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#f2f2f2\]:hover {
      background-color: #f2f2f2;
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      -webkit-text-decoration-line: underline;
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:underline-offset-4:hover {
      text-underline-offset: 4px;
    }
  }

  @media (min-width: 40rem) {
    .sm\:h-12 {
      height: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 40rem) {
    .sm\:w-auto {
      width: auto;
    }
  }

  @media (min-width: 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 40rem) {
    .sm\:items-start {
      align-items: flex-start;
    }
  }

  @media (min-width: 40rem) {
    .sm\:p-20 {
      padding: calc(var(--spacing) * 20);
    }
  }

  @media (min-width: 40rem) {
    .sm\:px-5 {
      padding-inline: calc(var(--spacing) * 5);
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-left {
      text-align: left;
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:w-\[158px\] {
      width: 158px;
    }
  }

  .dark\:border-white\/\[\.145\]:is(.dark *) {
    border-color: rgba(255, 255, 255, .145);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-white\/\[\.145\]:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-white) 14.5%, transparent);
    }
  }

  .dark\:bg-white\/\[\.06\]:is(.dark *) {
    background-color: rgba(255, 255, 255, .06);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-white\/\[\.06\]:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-white) 6%, transparent);
    }
  }

  .dark\:invert:is(.dark *) {
    --tw-invert: invert(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  @media (hover: hover) {
    .dark\:hover\:bg-\[\#1a1a1a\]:is(.dark *):hover {
      background-color: #1a1a1a;
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-\[\#ccc\]:is(.dark *):hover {
      background-color: #ccc;
    }
  }
}

@property --tw-animation-delay {
  syntax: "*";
  inherits: false;
  initial-value: 0s;
}

@property --tw-animation-direction {
  syntax: "*";
  inherits: false;
  initial-value: normal;
}

@property --tw-animation-duration {
  syntax: "*";
  inherits: false
}

@property --tw-animation-fill-mode {
  syntax: "*";
  inherits: false;
  initial-value: none;
}

@property --tw-animation-iteration-count {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

:root {
  --background: #fdfdfd;
  --foreground: #000;
  --card: #fdfdfd;
  --card-foreground: #000;
  --popover: #fcfcfc;
  --popover-foreground: #000;
  --primary: #7033ff;
  --primary-foreground: #fff;
  --secondary: #edf0f4;
  --secondary-foreground: #080808;
  --muted: #f5f5f5;
  --muted-foreground: #525252;
  --accent: #e2ebff;
  --accent-foreground: #1e69dc;
  --destructive: #e54b4f;
  --destructive-foreground: #fff;
  --border: #e7e7ee;
  --input: #ebebeb;
  --ring: #000;
  --chart-1: #4ac885;
  --chart-2: #7033ff;
  --chart-3: #fd822b;
  --chart-4: #3276e4;
  --chart-5: #747474;
  --sidebar: #f5f8fb;
  --sidebar-foreground: #000;
  --sidebar-primary: #000;
  --sidebar-primary-foreground: #fff;
  --sidebar-accent: #ebebeb;
  --sidebar-accent-foreground: #000;
  --sidebar-border: #ebebeb;
  --sidebar-ring: #000;
  --font-sans: Plus Jakarta Sans, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: IBM Plex Mono, monospace;
  --radius: 1rem;
  --shadow-2xs: 0px 2px 3px 0px rgba(0, 0, 0, .06);
  --shadow-xs: 0px 2px 3px 0px rgba(0, 0, 0, .06);
  --shadow-sm: 0px 2px 3px 0px rgba(0, 0, 0, .12), 0px 1px 2px -1px rgba(0, 0, 0, .12);
  --shadow: 0px 2px 3px 0px rgba(0, 0, 0, .12), 0px 1px 2px -1px rgba(0, 0, 0, .12);
  --shadow-md: 0px 2px 3px 0px rgba(0, 0, 0, .12), 0px 2px 4px -1px rgba(0, 0, 0, .12);
  --shadow-lg: 0px 2px 3px 0px rgba(0, 0, 0, .12), 0px 4px 6px -1px rgba(0, 0, 0, .12);
  --shadow-xl: 0px 2px 3px 0px rgba(0, 0, 0, .12), 0px 8px 10px -1px rgba(0, 0, 0, .12);
  --shadow-2xl: 0px 2px 3px 0px rgba(0, 0, 0, .3);
  --tracking-normal: 0em;
  --spacing: .24rem;
}

@supports (color: color(display-p3 0 0 0)) {
  :root {
    --background: color(display-p3 .992093 .992093 .992093);
    --foreground: color(display-p3 0 0 0);
    --card: color(display-p3 .992093 .992093 .992093);
    --card-foreground: color(display-p3 0 0 0);
    --popover: color(display-p3 .988276 .988276 .988276);
    --popover-foreground: color(display-p3 0 0 0);
    --primary: color(display-p3 .408799 .213174 .961964);
    --primary-foreground: color(display-p3 1 1 1);
    --secondary: color(display-p3 .931467 .9408 .955379);
    --secondary-foreground: color(display-p3 .0313661 .0313661 .0313661);
    --muted: color(display-p3 .960849 .960849 .960849);
    --muted-foreground: color(display-p3 .321563 .321564 .321564);
    --accent: color(display-p3 .892654 .920445 .992909);
    --accent-foreground: color(display-p3 .208321 .405806 .833322);
    --destructive: color(display-p3 .830453 .338979 .331245);
    --destructive-foreground: color(display-p3 1 1 1);
    --border: color(display-p3 .905895 .905895 .930948);
    --input: color(display-p3 .921607 .921607 .921607);
    --ring: color(display-p3 0 0 0);
    --chart-1: color(display-p3 .435281 .773989 .543573);
    --chart-2: color(display-p3 .408799 .213174 .961964);
    --chart-3: color(display-p3 .929737 .535517 .259848);
    --chart-4: color(display-p3 .268087 .456808 .864978);
    --chart-5: color(display-p3 .454938 .454938 .454938);
    --sidebar: color(display-p3 .96292 .972144 .982996);
    --sidebar-foreground: color(display-p3 0 0 0);
    --sidebar-primary: color(display-p3 0 0 0);
    --sidebar-primary-foreground: color(display-p3 1 1 1);
    --sidebar-accent: color(display-p3 .921607 .921607 .921607);
    --sidebar-accent-foreground: color(display-p3 0 0 0);
    --sidebar-border: color(display-p3 .921607 .921607 .921607);
    --sidebar-ring: color(display-p3 0 0 0);
  }
}

@supports (color: lab(0% 0 0)) {
  :root {
    --background: lab(99.304% -.0000298023 0);
    --foreground: lab(0% 0 0);
    --card: lab(99.304% -.0000298023 0);
    --card-foreground: lab(0% 0 0);
    --popover: lab(98.9676% -.0000298023 -.0000119209);
    --popover-foreground: lab(0% 0 0);
    --primary: lab(41.276% 61.6029 -92.3266);
    --primary-foreground: lab(100% 0 0);
    --secondary: lab(94.6509% -.49904 -2.31887);
    --secondary-foreground: lab(2.19295% -.00000745058 0);
    --muted: lab(96.5432% -.0000596046 0);
    --muted-foreground: lab(34.8776% -.0000149012 0);
    --accent: lab(92.8142% -.243574 -10.7981);
    --accent-foreground: lab(45.2906% 11.9466 -66.3225);
    --destructive: lab(55.0182% 60.4185 33.0921);
    --destructive-foreground: lab(100% 0 0);
    --border: lab(91.7977% .94524 -3.41178);
    --input: lab(93.0516% -.0000298023 0);
    --ring: lab(0% 0 0);
    --chart-1: lab(72.4642% -47.8952 22.9132);
    --chart-2: lab(41.276% 61.6029 -92.3266);
    --chart-3: lab(68.023% 44.2252 64.984);
    --chart-4: lab(49.9778% 8.92893 -63.243);
    --chart-5: lab(48.844% 0 0);
    --sidebar: lab(97.4119% -.624448 -1.79988);
    --sidebar-foreground: lab(0% 0 0);
    --sidebar-primary: lab(0% 0 0);
    --sidebar-primary-foreground: lab(100% 0 0);
    --sidebar-accent: lab(93.0516% -.0000298023 0);
    --sidebar-accent-foreground: lab(0% 0 0);
    --sidebar-border: lab(93.0516% -.0000298023 0);
    --sidebar-ring: lab(0% 0 0);
  }
}

.dark {
  --background: #231f20;
  --foreground: #fff;
  --card: #222327;
  --card-foreground: #f0f0f0;
  --popover: #222327;
  --popover-foreground: #f0f0f0;
  --primary: #545cec;
  --primary-foreground: #fff;
  --secondary: #37b475;
  --secondary-foreground: #f4fcfc;
  --muted: #2a2c33;
  --muted-foreground: #a0a0a0;
  --accent: #1e293b;
  --accent-foreground: #79c0ff;
  --destructive: #f87171;
  --destructive-foreground: #fff;
  --border: #33353a;
  --input: #33353a;
  --ring: #8c5cff;
  --chart-1: #4ade80;
  --chart-2: #8c5cff;
  --chart-3: #fca5a5;
  --chart-4: #5993f4;
  --chart-5: #a0a0a0;
  --sidebar: #161618;
  --sidebar-foreground: #f0f0f0;
  --sidebar-primary: #8c5cff;
  --sidebar-primary-foreground: #fff;
  --sidebar-accent: #2a2c33;
  --sidebar-accent-foreground: #8c5cff;
  --sidebar-border: #33353a;
  --sidebar-ring: #8c5cff;
  --font-sans: Plus Jakarta Sans, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: IBM Plex Mono, monospace;
  --radius: 1rem;
  --shadow-2xs: 0px 2px 3px 0px rgba(0, 0, 0, .06);
  --shadow-xs: 0px 2px 3px 0px rgba(0, 0, 0, .06);
  --shadow-sm: 0px 2px 3px 0px rgba(0, 0, 0, .12), 0px 1px 2px -1px rgba(0, 0, 0, .12);
  --shadow: 0px 2px 3px 0px rgba(0, 0, 0, .12), 0px 1px 2px -1px rgba(0, 0, 0, .12);
  --shadow-md: 0px 2px 3px 0px rgba(0, 0, 0, .12), 0px 2px 4px -1px rgba(0, 0, 0, .12);
  --shadow-lg: 0px 2px 3px 0px rgba(0, 0, 0, .12), 0px 4px 6px -1px rgba(0, 0, 0, .12);
  --shadow-xl: 0px 2px 3px 0px rgba(0, 0, 0, .12), 0px 8px 10px -1px rgba(0, 0, 0, .12);
  --shadow-2xl: 0px 2px 3px 0px rgba(0, 0, 0, .3);
}

@supports (color: color(display-p3 0 0 0)) {
  .dark {
    --background: color(display-p3 .134637 .122118 .125428);
    --foreground: color(display-p3 1 1 1);
    --card: color(display-p3 .134088 .13717 .151555);
    --card-foreground: color(display-p3 .941124 .941124 .941124);
    --popover: color(display-p3 .134088 .13717 .151555);
    --popover-foreground: color(display-p3 .941124 .941124 .941124);
    --primary: color(display-p3 .335296 .359882 .892627);
    --primary-foreground: color(display-p3 1 1 1);
    --secondary: color(display-p3 .369495 .696171 .479662);
    --secondary-foreground: color(display-p3 .962652 .987193 .987686);
    --muted: color(display-p3 .166144 .172308 .197594);
    --muted-foreground: color(display-p3 .627503 .627503 .627503);
    --accent: color(display-p3 .126427 .159551 .22552);
    --accent-foreground: color(display-p3 .538066 .745831 .978537);
    --destructive: color(display-p3 .906901 .474085 .459396);
    --destructive-foreground: color(display-p3 1 1 1);
    --border: color(display-p3 .201392 .207547 .225564);
    --input: color(display-p3 .201392 .207547 .225564);
    --ring: color(display-p3 .521732 .369051 .965242);
    --chart-1: color(display-p3 .468325 .858841 .53758);
    --chart-2: color(display-p3 .521732 .369051 .965242);
    --chart-3: color(display-p3 .939332 .662238 .654927);
    --chart-4: color(display-p3 .401524 .570695 .929352);
    --chart-5: color(display-p3 .627503 .627503 .627503);
    --sidebar: color(display-p3 .086324 .0863246 .0934086);
    --sidebar-foreground: color(display-p3 .941124 .941124 .941124);
    --sidebar-primary: color(display-p3 .521732 .369051 .965242);
    --sidebar-primary-foreground: color(display-p3 1 1 1);
    --sidebar-accent: color(display-p3 .166144 .172308 .197594);
    --sidebar-accent-foreground: color(display-p3 .521732 .369051 .965242);
    --sidebar-border: color(display-p3 .201392 .207547 .225564);
    --sidebar-ring: color(display-p3 .521732 .369051 .965242);
  }
}

@supports (color: lab(0% 0 0)) {
  .dark {
    --background: lab(12.2472% 2.15649 .0468552);
    --foreground: lab(100% 0 0);
    --card: lab(13.737% .30075 -2.8444);
    --card-foreground: lab(94.7916% -.0000298023 0);
    --popover: lab(13.737% .30075 -2.8444);
    --popover-foreground: lab(94.7916% -.0000298023 0);
    --primary: lab(45.2669% 31.8407 -75.2636);
    --primary-foreground: lab(100% 0 0);
    --secondary: lab(65.4758% -46.6302 21.9126);
    --secondary-foreground: lab(98.3594% -2.70906 -.959885);
    --muted: lab(18.0168% .439614 -4.84896);
    --muted-foreground: lab(65.8728% .0000298023 -.0000119209);
    --accent: lab(16.2244% -.476554 -13.2095);
    --accent-foreground: lab(74.8962% -9.93082 -38.5472);
    --destructive: lab(64.7405% 52.7187 26.66);
    --destructive-foreground: lab(100% 0 0);
    --border: lab(22.1025% .0234395 -3.45785);
    --input: lab(22.1025% .0234395 -3.45785);
    --ring: lab(51.3492% 47.9995 -75.7176);
    --chart-1: lab(79.1499% -56.2124 34.18);
    --chart-2: lab(51.3492% 47.9995 -75.7176);
    --chart-3: lab(76.6274% 33.0527 14.0383);
    --chart-4: lab(60.4488% 3.68816 -55.3263);
    --chart-5: lab(65.8728% .0000298023 -.0000119209);
    --sidebar: lab(7.31362% .375226 -1.37599);
    --sidebar-foreground: lab(94.7916% -.0000298023 0);
    --sidebar-primary: lab(51.3492% 47.9995 -75.7176);
    --sidebar-primary-foreground: lab(100% 0 0);
    --sidebar-accent: lab(18.0168% .439614 -4.84896);
    --sidebar-accent-foreground: lab(51.3492% 47.9995 -75.7176);
    --sidebar-border: lab(22.1025% .0234395 -3.45785);
    --sidebar-ring: lab(51.3492% 47.9995 -75.7176);
  }
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

/*# sourceMappingURL=%5Broot-of-the-server%5D__8ebb6d4b._.css.map*/