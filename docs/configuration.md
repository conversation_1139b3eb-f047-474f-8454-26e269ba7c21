# Configuration

This guide covers the configuration options for the ACME Corporation Backstage application.

## App Configuration

The main configuration is stored in `app-config.yaml` and `app-config.production.yaml`.

### Basic Settings

```yaml
app:
  title: ACME Corp. Backstage App
  baseUrl: http://localhost:3000

organization:
  name: ACME Corporation
```

### Backend Configuration

```yaml
backend:
  baseUrl: http://localhost:7007
  listen:
    port: 7007
  database:
    client: pg
    connection:
      host: 127.0.0.1
      port: 5432
      user: postgres
      password: ${DB_PASSWORD}
```

### TechDocs Configuration

```yaml
techdocs:
  builder: 'local'  # Use 'local' for development, 'external' for production
  generator:
    runIn: 'docker'
  publisher:
    type: 'local'
```

### Authentication

```yaml
auth:
  providers:
    guest: {}
    github:
      development:
        clientId: ${GITHUB_CLIENT_ID}
        clientSecret: ${GITHUB_CLIENT_SECRET}
    google:
      development:
        clientId: ${AUTH_GOOGLE_CLIENT_ID}
        clientSecret: ${AUTH_GOOGLE_CLIENT_SECRET}
```

### Catalog Configuration

```yaml
catalog:
  locations:
    - type: file
      target: ../../examples/entities.yaml
    - type: file
      target: ../../examples/template/template.yaml
      rules:
        - allow: [Template]
```

## Environment Variables

Create a `.env` file with the following variables:

```env
# Database
DB_PASSWORD=your-postgres-password

# GitHub Integration
GITHUB_TOKEN=your-github-token

# GitHub OAuth
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Google OAuth
AUTH_GOOGLE_CLIENT_ID=your-google-client-id
AUTH_GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## Production Configuration

For production deployments, use `app-config.production.yaml` to override development settings.

## Plugin Configuration

### Kubernetes Plugin

```yaml
kubernetes:
  # Configure Kubernetes integration
```

### Permission Framework

```yaml
permission:
  enabled: true
```

## Troubleshooting

- Check environment variables are set correctly
- Verify database connection
- Ensure Docker is running for TechDocs
- Check plugin configurations
