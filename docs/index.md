# ACME Corporation Backstage

Welcome to the ACME Corporation Backstage application documentation!

## Overview

This Backstage instance serves as the central hub for ACME Corporation's developer portal, providing:

- **Service Catalog**: Discover and manage all services, APIs, and resources
- **Software Templates**: Quickly scaffold new projects with best practices
- **TechDocs**: Centralized documentation for all your services
- **Authentication**: Integrated with GitHub and Google OAuth

## Quick Links

- [Getting Started](getting-started.md) - Set up your development environment
- [Configuration](configuration.md) - Learn about app configuration options

## Features

### 🏗️ Software Templates

We provide several templates to help you get started:

- **Example Template**: Basic service template
- **BHVR Stack Template**: Full-stack TypeScript application with <PERSON>un, Hono, Vite, and React

### 📚 TechDocs

This documentation system is powered by TechDocs, which allows you to:

- Write documentation in Markdown
- Keep docs close to your code
- Automatically publish and update documentation

### 🔐 Authentication

Supported authentication providers:

- **Guest Access**: For quick exploration
- **GitHub OAuth**: For GitHub users
- **Google OAuth**: For Google Workspace users

## Getting Help

If you encounter any issues or have questions:

1. Check the [Getting Started](getting-started.md) guide
2. Review the [Configuration](configuration.md) documentation
3. Contact the platform team

## Architecture

This Backstage instance includes:

- **Frontend**: React-based UI with custom plugins
- **Backend**: Node.js backend with PostgreSQL database
- **Plugins**: Catalog, TechDocs, Scaffolder, Search, and more

---

*Last updated: $(date)*
