# Getting Started

This guide will help you get up and running with the ACME Corporation Backstage application.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (version 18 or higher)
- **Yarn** package manager
- **Docker** (for TechDocs generation)
- **PostgreSQL** (for the database)

## Development Setup

### 1. <PERSON>lone the Repository

```bash
git clone <repository-url>
cd backstage-latest
```

### 2. Install Dependencies

```bash
yarn install
```

### 3. Environment Configuration

Create a `.env` file in the root directory:

```bash
cp .env.example .env
```

Configure the following environment variables:

```env
# Database
DB_PASSWORD=your-postgres-password

# GitHub Integration
GITHUB_TOKEN=your-github-token

# GitHub OAuth (optional)
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Google OAuth (optional)
AUTH_GOOGLE_CLIENT_ID=your-google-client-id
AUTH_GOOGLE_CLIENT_SECRET=your-google-client-secret
```

### 4. Database Setup

Start PostgreSQL and create a database:

```bash
# Using Docker
docker run --name backstage-postgres \
  -e POSTGRES_PASSWORD=your-password \
  -e POSTGRES_DB=backstage \
  -p 5432:5432 \
  -d postgres:13
```

### 5. Start the Application

```bash
# Start the backend
yarn dev

# In another terminal, start the frontend
yarn start
```

The application will be available at:
- **Frontend**: http://localhost:3000
- **Backend**: http://localhost:7007

## First Steps

### 1. Explore the Catalog

Navigate to the **Catalog** to see registered components, APIs, and systems.

### 2. Try Software Templates

Go to **Create** to scaffold new projects using our templates:

- **Example Template**: Basic service
- **BHVR Template**: Full-stack TypeScript application

### 3. Browse Documentation

Visit **Docs** to explore TechDocs for various services.

## Authentication

### Guest Access

For quick exploration, you can use guest access without authentication.

### GitHub OAuth

To set up GitHub OAuth:

1. Create a GitHub OAuth App in your organization
2. Set the authorization callback URL to: `http://localhost:7007/api/auth/github/handler/frame`
3. Add the client ID and secret to your `.env` file

### Google OAuth

To set up Google OAuth:

1. Create a Google OAuth 2.0 client in Google Cloud Console
2. Add `http://localhost:7007/api/auth/google/handler/frame` as an authorized redirect URI
3. Add the client ID and secret to your `.env` file

## Troubleshooting

### Common Issues

**Port already in use**
```bash
# Kill processes using the ports
lsof -ti:3000 | xargs kill -9
lsof -ti:7007 | xargs kill -9
```

**Database connection issues**
- Ensure PostgreSQL is running
- Check your database credentials in `.env`
- Verify the database exists

**TechDocs not loading**
- Ensure Docker is running
- Check that `techdocs.builder` is set to `'local'` in development

### Getting Help

If you continue to experience issues:

1. Check the console logs for error messages
2. Verify all environment variables are set correctly
3. Ensure all prerequisites are installed and running
4. Contact the platform team for assistance

## Next Steps

- [Configuration Guide](configuration.md) - Learn about advanced configuration options
- Add your first service to the catalog
- Create documentation for your services
- Explore available plugins and integrations
