{"name": "backend", "version": "0.0.0", "main": "dist/index.cjs.js", "types": "src/index.ts", "private": true, "backstage": {"role": "backend"}, "scripts": {"start": "NODE_OPTIONS=\"--no-node-snapshot --enable-source-maps\" backstage-cli package start", "dev": "nodemon --watch src --ext ts,tsx --exec \"backstage-cli package start\"", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "build-image": "docker build ../.. -f <PERSON><PERSON><PERSON><PERSON> --tag backstage"}, "dependencies": {"@backstage-community/plugin-entity-feedback-backend": "^0.10.0", "@backstage/backend-defaults": "^0.11.0", "@backstage/config": "^1.3.2", "@backstage/plugin-app-backend": "^0.5.3", "@backstage/plugin-auth-backend": "^0.25.1", "@backstage/plugin-auth-backend-module-github-provider": "^0.3.4", "@backstage/plugin-auth-backend-module-google-provider": "^0.3.4", "@backstage/plugin-auth-backend-module-guest-provider": "^0.2.9", "@backstage/plugin-auth-node": "^0.6.4", "@backstage/plugin-catalog-backend": "^2.1.0", "@backstage/plugin-catalog-backend-module-logs": "^0.1.11", "@backstage/plugin-catalog-backend-module-scaffolder-entity-model": "^0.2.9", "@backstage/plugin-kubernetes-backend": "^0.19.7", "@backstage/plugin-permission-backend": "^0.7.1", "@backstage/plugin-permission-backend-module-allow-all-policy": "^0.2.9", "@backstage/plugin-permission-common": "^0.9.0", "@backstage/plugin-permission-node": "^0.10.1", "@backstage/plugin-proxy-backend": "^0.6.3", "@backstage/plugin-scaffolder-backend": "^2.0.0", "@backstage/plugin-scaffolder-backend-module-github": "^0.8.0", "@backstage/plugin-search-backend": "^2.0.3", "@backstage/plugin-search-backend-module-catalog": "^0.3.5", "@backstage/plugin-search-backend-module-pg": "^0.5.45", "@backstage/plugin-search-backend-module-techdocs": "^0.4.3", "@backstage/plugin-search-backend-node": "^1.3.12", "@backstage/plugin-techdocs-backend": "^2.0.3", "app": "link:../app", "better-sqlite3": "^9.0.0", "dotenv": "^17.2.0", "node-gyp": "^10.0.0", "pg": "^8.11.3"}, "devDependencies": {"@backstage/cli": "^0.33.0", "nodemon": "^3.1.10"}, "files": ["dist"]}