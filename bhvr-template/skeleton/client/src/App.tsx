import { useState } from 'react'
import { ApiResponse } from 'shared'
import './App.css'

const SERVER_URL = import.meta.env.VITE_SERVER_URL || "http://localhost:${{ values.serverPort }}"

function App() {
  const [data, setData] = useState<ApiResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  async function sendRequest() {
    setLoading(true)
    setError(null)
    
    try {
      const req = await fetch(`${SERVER_URL}/hello`)
      if (!req.ok) {
        throw new Error(`HTTP error! status: ${req.status}`)
      }
      const res: ApiResponse = await req.json()
      setData(res)
    } catch (error) {
      console.error('Request failed:', error)
      setError(error instanceof Error ? error.message : 'Unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="app">
      <header className="app-header">
        <div className="logo">
          <h1>${{ values.name }}</h1>
          <p className="tagline">${{ values.description }}</p>
        </div>
        
        <div className="stack-info">
          <h2>🚀 BHVR Stack</h2>
          <div className="stack-grid">
            <div className="stack-item">
              <strong>B</strong>un - Fast JavaScript runtime
            </div>
            <div className="stack-item">
              <strong>H</strong>ono - Lightweight web framework
            </div>
            <div className="stack-item">
              <strong>V</strong>ite - Fast build tool
            </div>
            <div className="stack-item">
              <strong>R</strong>eact - Modern UI library
            </div>
          </div>
        </div>

        <div className="api-section">
          <h3>Test API Connection</h3>
          <button 
            onClick={sendRequest} 
            disabled={loading}
            className="api-button"
          >
            {loading ? 'Loading...' : 'Call API'}
          </button>

          {error && (
            <div className="error">
              <h4>❌ Error:</h4>
              <p>{error}</p>
            </div>
          )}

          {data && (
            <div className="response">
              <h4>✅ Response:</h4>
              <div className="response-content">
                <p><strong>Message:</strong> {data.message}</p>
                <p><strong>Success:</strong> {data.success.toString()}</p>
                {data.data && (
                  <details>
                    <summary>Additional Data</summary>
                    <pre>{JSON.stringify(data.data, null, 2)}</pre>
                  </details>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="links">
          <h3>🔗 Quick Links</h3>
          <div className="link-grid">
            <a href={`${SERVER_URL}`} target="_blank" rel="noopener noreferrer">
              Backend Server
            </a>
            <a href={`${SERVER_URL}/api/health`} target="_blank" rel="noopener noreferrer">
              Health Check
            </a>
            <a href="https://github.com/stevedylandev/bhvr" target="_blank" rel="noopener noreferrer">
              BHVR Documentation
            </a>
            <a href="https://backstage.io/" target="_blank" rel="noopener noreferrer">
              Backstage
            </a>
          </div>
        </div>

        <footer className="footer">
          <p>Generated with ❤️ by Backstage Scaffolder</p>
          <p>Powered by the BHVR stack</p>
        </footer>
      </header>
    </div>
  )
}

export default App
