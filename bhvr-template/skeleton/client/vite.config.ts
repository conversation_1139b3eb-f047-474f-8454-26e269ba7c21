import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: ${{ values.clientPort }},
    proxy: {
      '/api': {
        target: 'http://localhost:${{ values.serverPort }}',
        changeOrigin: true,
      },
    },
  },
  resolve: {
    alias: {
      'shared': path.resolve(__dirname, '../shared/src'),
    },
  },
  define: {
    'import.meta.env.VITE_SERVER_URL': JSON.stringify(
      process.env.VITE_SERVER_URL || 'http://localhost:${{ values.serverPort }}'
    ),
  },
})
