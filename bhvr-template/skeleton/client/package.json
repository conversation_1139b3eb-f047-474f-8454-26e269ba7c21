{"name": "client", "version": "1.0.0", "description": "React frontend for ${{ values.name }}", "type": "module", "scripts": {"dev": "vite --port ${{ values.clientPort }}", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "test": "vitest"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "shared": "workspace:*"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "typescript": "^5.0.0", "vite": "^4.4.0", "vitest": "^0.34.0"}}