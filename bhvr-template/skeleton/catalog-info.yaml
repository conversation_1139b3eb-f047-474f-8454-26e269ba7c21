apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: ${{ values.name }}
  description: ${{ values.description }}
  annotations:
    github.com/project-slug: ${{ values.destination.owner }}/${{ values.destination.repo }}
    backstage.io/techdocs-ref: dir:.
  tags:
    - bhvr
    - typescript
    - react
    - hono
    - bun
    - vite
    - monorepo
    - fullstack
  links:
    - url: https://github.com/${{ values.destination.owner }}/${{ values.destination.repo }}
      title: Repository
      icon: github
    - url: http://localhost:${{ values.clientPort }}
      title: Frontend (Development)
      icon: web
    - url: http://localhost:${{ values.serverPort }}
      title: Backend API (Development)
      icon: api
spec:
  type: service
  lifecycle: experimental
  owner: ${{ values.owner }}
  system: ${{ values.name }}-system
  providesApis:
    - ${{ values.name }}-api
  dependsOn:
    - resource:${{ values.name }}-database

---
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: ${{ values.name }}-api
  description: API for ${{ values.name }}
  tags:
    - rest
    - hono
    - typescript
spec:
  type: openapi
  lifecycle: experimental
  owner: ${{ values.owner }}
  system: ${{ values.name }}-system
  definition: |
    openapi: 3.0.0
    info:
      title: ${{ values.name }} API
      description: ${{ values.description }}
      version: 1.0.0
    servers:
      - url: http://localhost:${{ values.serverPort }}
        description: Development server
    paths:
      /:
        get:
          summary: Health check
          responses:
            '200':
              description: Server is running
              content:
                text/plain:
                  schema:
                    type: string
                    example: "Hello Hono!"
      /hello:
        get:
          summary: Hello endpoint
          responses:
            '200':
              description: Success response
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      message:
                        type: string
                        example: "Hello BHVR!"
                      success:
                        type: boolean
                        example: true

---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: ${{ values.name }}-system
  description: ${{ values.name }} system
spec:
  owner: ${{ values.owner }}

{% if values.enableDatabase %}
---
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  name: ${{ values.name }}-database
  description: Database for ${{ values.name }}
  tags:
    - database
    - ${{ values.databaseType }}
spec:
  type: database
  owner: ${{ values.owner }}
  system: ${{ values.name }}-system
{% endif %}
