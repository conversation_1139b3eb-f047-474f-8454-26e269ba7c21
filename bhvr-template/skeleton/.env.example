# Server Configuration
PORT=${{ values.serverPort }}
NODE_ENV=development

# Client Configuration
VITE_SERVER_URL=http://localhost:${{ values.serverPort }}

{% if values.enableDatabase %}
# Database Configuration
DB_HOST=localhost
DB_PORT={% if values.databaseType == "postgresql" %}5432{% elif values.databaseType == "mysql" %}3306{% else %}N/A{% endif %}
DB_NAME=${{ values.name }}
DB_USER=your_username
DB_PASSWORD=your_password
{% endif %}

# Add your environment variables here
# API_KEY=your_api_key
# JWT_SECRET=your_jwt_secret
