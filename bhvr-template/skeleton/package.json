{"name": "${{ values.name }}", "version": "1.0.0", "description": "${{ values.description }}", "private": true, "workspaces": ["client", "server", "shared"], "scripts": {"dev": "bun run dev:shared & bun run dev:server & bun run dev:client", "dev:shared": "cd shared && bun run dev", "dev:server": "cd server && bun run dev", "dev:client": "cd client && bun run dev", "build": "bun run build:shared && bun run build:client", "build:shared": "cd shared && bun run build", "build:client": "cd client && bun run build", "clean": "rm -rf node_modules client/node_modules server/node_modules shared/node_modules", "lint": "cd client && bun run lint", "type-check": "cd client && bun run type-check && cd ../server && bun run type-check", "test": "cd client && bun run test"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"bun": ">=1.0.0"}, "keywords": ["bhvr", "bun", "hono", "vite", "react", "typescript", "monorepo", "fullstack"], "author": "${{ values.owner }}", "license": "MIT"}