# ${{ values.name }} Server

Hono backend server for ${{ values.name }}.

## 🚀 Getting Started

### Install dependencies
```sh
bun install
```

### Run development server
```sh
bun run dev
```

The server will start at http://localhost:${{ values.serverPort }}

## 📁 Project Structure

```
server/
├── src/
│   ├── index.ts          # Main server file
│   ├── routes/           # API route handlers
│   ├── middleware/       # Custom middleware
│   ├── services/         # Business logic
│   ├── models/           # Data models
│   └── utils/            # Utility functions
├── package.json
├── tsconfig.json
└── README.md
```

## 🛠️ Available Scripts

- `bun run dev` - Start development server with hot reload
- `bun run start` - Start production server
- `bun run build` - Build for production
- `bun run type-check` - Check TypeScript types
- `bun run test` - Run tests

## 🔗 API Endpoints

### Health Check
- `GET /` - Basic health check
- `GET /api/health` - Detailed health information

### Application
- `GET /hello` - Hello world endpoint

{% if values.enableDatabase %}
## 🗄️ Database

This server is configured to use **${{ values.databaseType }}**.

### Configuration

Database configuration can be found in the environment variables:

```env
DB_HOST=localhost
DB_PORT={% if values.databaseType == "postgresql" %}5432{% elif values.databaseType == "mysql" %}3306{% else %}N/A{% endif %}
DB_NAME=${{ values.name }}
DB_USER=your_username
DB_PASSWORD=your_password
```

### Connection

The database connection is established in `src/config/database.ts`.
{% endif %}

## 🌍 Environment Variables

Create a `.env` file in the server directory:

```env
PORT=${{ values.serverPort }}
NODE_ENV=development
{% if values.enableDatabase %}
# Database
DB_HOST=localhost
DB_PORT={% if values.databaseType == "postgresql" %}5432{% elif values.databaseType == "mysql" %}3306{% else %}N/A{% endif %}
DB_NAME=${{ values.name }}
DB_USER=your_username
DB_PASSWORD=your_password
{% endif %}
```

## 🧪 Testing

```sh
bun run test
```

## 📝 Development Notes

- The server uses Hono for routing and middleware
- TypeScript is configured for strict type checking
- Shared types are imported from the `shared` package
- Hot reload is enabled in development mode
