{"name": "server", "version": "1.0.0", "description": "Hono backend server for ${{ values.name }}", "main": "src/index.ts", "scripts": {"dev": "bun run --hot src/index.ts", "start": "bun run src/index.ts", "build": "bun build src/index.ts --outdir dist --target bun", "type-check": "tsc --noEmit", "test": "bun test"}, "dependencies": {"hono": "^3.12.0", "shared": "workspace:*"}, "devDependencies": {"@types/bun": "latest", "typescript": "^5.0.0"}}