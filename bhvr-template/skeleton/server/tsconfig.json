{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "types": ["bun-types"], "baseUrl": ".", "paths": {"shared/*": ["../shared/src/*"], "shared": ["../shared/src/index.ts"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}