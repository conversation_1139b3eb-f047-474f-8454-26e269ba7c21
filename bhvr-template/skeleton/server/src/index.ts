import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'
import type { ApiResponse } from 'shared'

const app = new Hono()

// Middleware
app.use('*', cors())
app.use('*', logger())
app.use('*', prettyJSON())

// Health check endpoint
app.get('/', (c) => {
  return c.text('Hello Hono!')
})

// API endpoints
app.get('/hello', async (c) => {
  const data: ApiResponse = {
    message: "Hello BHVR!",
    success: true,
    data: {
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      environment: process.env.NODE_ENV || "development"
    }
  }
  return c.json(data, { status: 200 })
})

// API routes group
const api = app.basePath('/api')

api.get('/health', (c) => {
  const response: ApiResponse = {
    message: "Server is healthy",
    success: true,
    data: {
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
      memory: process.memoryUsage()
    }
  }
  return c.json(response)
})

// Error handling
app.onError((err, c) => {
  console.error('Server error:', err)
  const response: ApiResponse = {
    message: "Internal server error",
    success: false,
    error: err.message
  }
  return c.json(response, 500)
})

// 404 handler
app.notFound((c) => {
  const response: ApiResponse = {
    message: "Route not found",
    success: false,
    error: `${c.req.method} ${c.req.path} not found`
  }
  return c.json(response, 404)
})

const port = process.env.PORT || ${{ values.serverPort }}

console.log(`🚀 Server starting on port ${port}`)

export default {
  port,
  fetch: app.fetch,
}
