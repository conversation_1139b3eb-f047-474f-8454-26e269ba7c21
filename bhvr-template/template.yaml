apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: bhvr-app
  title: BHVR Full-Stack Application
  description: Create a new BHVR (Bun + Hono + Vite + React) full-stack application with TypeScript monorepo structure
  tags:
    - recommended
    - typescript
    - react
    - hono
    - bun
    - vite
    - monorepo
    - fullstack
spec:
  owner: platform-team
  type: service
  parameters:
    - title: Basic Information
      required:
        - name
        - description
        - owner
      properties:
        name:
          title: Name
          type: string
          description: Unique name of the BHVR application
          pattern: '^[a-zA-Z0-9-_]+$'
          ui:autofocus: true
          ui:help: 'Use lowercase letters, numbers, hyphens, and underscores only'
        description:
          title: Description
          type: string
          description: A brief description of your BHVR application
          ui:widget: textarea
          ui:options:
            rows: 3
        owner:
          title: Owner
          type: string
          description: Owner of the component
          ui:field: OwnerPicker
          ui:options:
            catalogFilter:
              kind: Group

    - title: Repository Configuration
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repository Location
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - github.com
              - gitlab.com

    - title: Application Configuration
      properties:
        serverPort:
          title: Server Port
          type: number
          description: Port number for the Hono backend server
          default: 3000
          minimum: 1000
          maximum: 65535
        clientPort:
          title: Client Port
          type: number
          description: Port number for the Vite development server
          default: 5173
          minimum: 1000
          maximum: 65535
        enableDatabase:
          title: Enable Database Support
          type: boolean
          description: Include database configuration and examples
          default: false
        databaseType:
          title: Database Type
          type: string
          description: Type of database to configure
          default: postgresql
          enum:
            - postgresql
            - mysql
            - sqlite
          enumNames:
            - PostgreSQL
            - MySQL
            - SQLite
          ui:widget: select
          ui:options:
            condition:
              enableDatabase: true

  steps:
    - id: fetch-base
      name: Fetch Base Template
      action: fetch:template
      input:
        url: ./skeleton
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          owner: ${{ parameters.owner }}
          serverPort: ${{ parameters.serverPort }}
          clientPort: ${{ parameters.clientPort }}
          enableDatabase: ${{ parameters.enableDatabase }}
          databaseType: ${{ parameters.databaseType }}
          destination: ${{ parameters.repoUrl | parseRepoUrl }}

    - id: publish
      name: Publish to Repository
      action: publish:github
      input:
        description: ${{ parameters.description }}
        repoUrl: ${{ parameters.repoUrl }}
        defaultBranch: main
        gitCommitMessage: 'Initial commit: BHVR application scaffolded by Backstage'
        gitAuthorName: 'Backstage Scaffolder'
        gitAuthorEmail: '<EMAIL>'

    - id: register
      name: Register in Catalog
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps['publish'].output.repoContentsUrl }}
        catalogInfoPath: '/catalog-info.yaml'

  output:
    links:
      - title: Repository
        url: ${{ steps['publish'].output.remoteUrl }}
        icon: github
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps['register'].output.entityRef }}
    text:
      - title: Getting Started
        content: |
          ## Your BHVR application has been created! 🎉

          **Repository:** ${{ steps['publish'].output.remoteUrl }}

          ### Quick Start Commands:
          ```bash
          # Clone the repository
          git clone ${{ steps['publish'].output.remoteUrl }}
          cd ${{ parameters.name }}

          # Install dependencies
          bun install

          # Start development servers
          bun run dev
          ```

          ### What's included:
          - 🚀 **Bun** - Fast JavaScript runtime and package manager
          - 🔥 **Hono** - Lightweight web framework for the backend
          - ⚡ **Vite** - Fast build tool for the frontend
          - ⚛️ **React** - Modern UI library
          - 📦 **Monorepo** - Organized workspace structure
          - 🔒 **TypeScript** - Type-safe development
          - 🔗 **Shared Types** - Type sharing between client and server

          ### Development URLs:
          - Frontend: http://localhost:${{ parameters.clientPort }}
          - Backend: http://localhost:${{ parameters.serverPort }}

          Happy coding! 🚀
